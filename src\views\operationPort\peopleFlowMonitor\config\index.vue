<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <div class="search-from">
        <el-input v-model="searchFrom.name" placeholder="名称" clearable style="width: 200px"></el-input>
        <el-select ref="treeSelect" v-model="searchForm.regionCode" placeholder="所在楼层" clearable @clear="handleClear">
          <el-option hidden :value="searchForm.regionCode" :label="regionName"> </el-option>
          <el-tree :data="spaceData" :props="spaceProps" :expand-on-click-node="false" :check-on-click-node="true" @node-click="handleNodeClick"> </el-tree>
        </el-select>
        <div style="display: inline-block">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
          <el-button style="position: absolute; right: 0px; top: calc(50% + 5px); transform: translateY(-50%)" type="primary" icon="el-icon-plus" @click="control('add')">
            新增
          </el-button>
        </div>
      </div>
    </div>
    <div slot="content" class="table-content">
      <TablePage
        ref="table"
        v-loading="tableLoading"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 40px)"
        :pageData="pageData"
        :pageProps="pageProps"
        @pagination="paginationChange"
      >
      </TablePage>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import { transData } from '@/util'
export default {
  name: 'config',
  data() {
    return {
      isDrawer: false,
      tableLoading: false,
      searchFrom: {
        name: '',
        regionCode: ''
      },
      regionName: '',
      tableColumn: [
        {
          width: 180,
          prop: 'name',
          label: '名称'
        },
        {
          prop: 'enterNum',
          label: '进入人次'
        },
        {
          prop: 'leaveNum',
          label: '离开人次'
        },
        {
          prop: 'deviceNum',
          label: '监测设备数',
          render: (h, row) => {
            return row.row.list.length ? (
              <el-tooltip effect="dark" placement="top-start">
                <span>{row.row.deviceNum}</span>
                <span slot="content" style="max-width: 200px; word-break: break-all; white-space: normal; display: block;">
                  {row.row.list.join(',')}
                </span>
              </el-tooltip>
            ) : (
              <span>{row.row.deviceNum}</span>
            )
          }
        },
        {
          prop: 'regionName',
          label: '所在楼层'
        },
        {
          prop: 'isEntryExit',
          label: '出入口',
          formatter: (scope) => {
            return scope.row.isEntryExit == 0 ? '是' : scope.row.isEntryExit == 1 ? '否' : ''
          }
        },
        {
          width: 140,
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" style="color: #3562DB" onClick={() => this.control('detail', row.row)}>
                  查看
                </span>
                <span class="operationBtn-span" style="color: #3562DB" onClick={() => this.control('edit', row.row)}>
                  编辑
                </span>
                <span class="operationBtn-span" style="color: #FF4D4F" onClick={() => this.control('del', row.row)}>
                  删除
                </span>
              </div>
            )
          }
        }
      ],
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      },
      spaceData: [],
      spaceProps: {
        label: 'ssmName',
        isLeaf: 'leaf',
        children: 'children'
      }
    }
  },
  watch: {},
  mounted() {
    this.spaceTreeListFn()
    this.getConfigList()
  },
  methods: {
    // 查询
    searchForm() {
      this.getConfigList()
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.searchForm()
    },
    control(type, row) {
      if (['add', 'edit'].includes(type)) {
        let query = type !== 'add' ? { id: row?.id ?? '' } : {}
        this.$router.push({
          path: '/peopleFlowMonitor/config/add',
          query: {
            type,
            ...query
          }
        })
      } else if (type == 'detail') {
        // 详情
        this.$router.push({
          path: '/peopleFlowMonitor/config/add',
          query: {
            type,
            id: row?.id ?? ''
          }
        })
      } else if (type == 'del') {
        // 删除
        this.$confirm('确认要删除该条信息吗？删除后将无法恢复。', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '删除',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.delPeopleFlowMonitorConfig({ id: row.id }).then((res) => {
            if (res.code == 200 && res.data) {
              this.$message({ message: '删除成功', type: 'success' })
              this.isLastPage(1)
              this.searchForm()
            } else {
              this.$message({ message: res.msg, type: 'error' })
            }
          })
        })
      }
    },
    // 获取配置列表
    getConfigList() {
      let param = {
        ...this.searchFrom,
        pageSize: this.pageData.size,
        page: this.pageData.current
      }
      this.tableLoading = true
      this.$api
        .getPeopleFlowMonitorConfigList(param)
        .then((res) => {
          this.tableData = res.records
          this.pageData.total = res.total
          this.tableLoading = false
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    // 空间数据清除
    handleClear() {
      this.searchForm.regionCode = ''
      this.regionName = ''
    },
    // 选择下拉树 数据
    handleNodeClick(data) {
      this.searchForm.regionCode = data.id
      this.regionName = data.ssmName
      this.$refs.treeSelect.blur()
    },
    spaceTreeListFn() {
      this.$api.spaceTreeList().then((res) => {
        this.treeLoading = false
        if (res.code == 200) {
          this.spaceData = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    // 判断当前页是否是最后一页
    isLastPage(deleteNum) {
      let deleteAfterPage = Math.ceil((this.pageData.total - deleteNum) / this.pageData.size)
      let currentPage = this.pageData.current > deleteAfterPage ? deleteAfterPage : this.pageData.current
      this.pageData.current = currentPage < 1 ? 1 : currentPage
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getConfigList()
    }
  }
}
</script>
<style lang="scss" scoped>
.control-btn-header {
  padding: 0px 10px 10px 10px !important;
  .search-from {
    padding-right: 180px;
    position: relative;
    & > div {
      margin-top: 10px;
      margin-right: 10px;
    }
  }
}
.table-content {
  margin-top: 15px;
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
  ::v-deep(.content-drawer) {
    width: 40% !important;
    .el-drawer__header {
      height: 56px;
      margin: 0px;
      padding: 0px 26px;
      font-size: 18px;
      color: #333333;
      line-height: 18px;
    }
    .el-drawer__body {
      background: #f6f5fa;
      padding: 24px;
      div {
        width: 100%;
        height: 100%;
        background: #fff;
        padding: 16px;
        border-radius: 4px;
        font-weight: 500;
        font-size: 14px;
        color: #333333;
        word-wrap: break-word;
        word-break: break-all;
        overflow-x: auto;
        p {
          margin: 0px;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.operationBtn-span {
  margin-right: 10px;
}
</style>
