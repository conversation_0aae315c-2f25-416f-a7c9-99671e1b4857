<!DOCTYPE html>
<html lang="zh">

<head>
  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0" />
  <link rel="icon" href="/favicon.ico" />
  <meta name="keywords" content="后台系统,管理后台" />
  <meta name="description" content="智慧医院后勤综合监管平台" />
  <title>
    <%= VUE_APP_TITLE %>
  </title>
  <!-- 通用图标 -->
  <link href="/meta2dStatic/icon/GenericIcon/iconfont.css" rel="stylesheet" />
  <!-- meta2d-2d -->
  <link href="/meta2dStatic/icon/2d/iconfont.css" rel="stylesheet" />
  <!-- 国家电网 -->
  <link href="/meta2dStatic/icon/StateGrid/iconfont.css" rel="stylesheet" />
  <!-- 电气 -->
  <link href="/meta2dStatic/icon/lectricalEngineering/iconfont.css" rel="stylesheet" />
  <style>
    .back-stage-home {
      position: absolute;
      z-index: 10000;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      user-select: none;
      color: #736477;
      background-color: snow;
    }

    .back-stage-home .loading {
      position: relative;
      display: inline-block;
      width: 1em;
      height: 1em;
      color: inherit;
      vertical-align: middle;
      pointer-events: none;
      transform: scale(3);
    }

    .back-stage-home .loading::before,
    .back-stage-home .loading::after {
      content: '';
      display: block;
      position: absolute;
      background-color: currentColor;
      left: 50%;
      right: 0;
      top: 0;
      bottom: 50%;
      box-shadow: -0.5em 0 0 currentColor;
      animation: loading 1s linear infinite;
    }

    .back-stage-home .loading::after {
      top: 50%;
      bottom: 0;
      animation-delay: 0.25s;
    }

    @keyframes loading {

      0%,
      100% {
        box-shadow: -0.5em 0 0 transparent;
        background-color: currentColor;
      }

      50% {
        box-shadow: -0.5em 0 0 currentColor;
        background-color: transparent;
      }
    }

    .back-stage-home .text {
      font-size: 24px;
      margin-top: 50px;
    }
  </style>
  <script>
    window.jsDiagramNum = 613 //1450
    //电力 749
    //物联网 613
    //其他 88
    //所有 1450
    window.companyName = ''
    window.userDefinedDiagram = null
  </script>
</head>

<body>
  <noscript>
    <strong>We're sorry but <%= VUE_APP_TITLE %> doesn't work properly without JavaScript enabled. Please enable it to
        continue.</strong>
  </noscript>
  <div id="app">
    <div class="back-stage-home">
      <div class="loading"></div>
      <div class="text">
        <%= VUE_APP_TITLE %>载入中…
      </div>
    </div>
  </div>
  <script defer src="/meta2dStatic/diagram/964909.1.js"></script>
  <script defer src="/meta2dStatic/diagram/964909.2.js"></script>
  <script defer src="/meta2dStatic/diagram/964909.3.js"></script>
  <script defer src="/meta2dStatic/diagram/964909.4.js"></script>
  <script defer src="/meta2dStatic/diagram/964909.5.js"></script>
  <script defer src="/meta2dStatic/diagram/964909.6.js"></script>
  <script defer src="/meta2dStatic/diagram/964909.7.js"></script>
  <script defer src="/meta2dStatic/diagram/964909.8.js"></script>
  <script defer src="/meta2dStatic/diagram/964909.9.js"></script>
  <script src="/meta2dStatic/diagram/rg.js"></script>
  <script type="module" src="/src/main.js"></script>
  <script src="/config.js"></script>
  <script>
    // 获取当前医院环境
    const currentEnv = '<%= VUE_APP_HOSPITAL_NODE_ENV %>'
    const hospitalConfig = window.__CONFIG__.hospitalConfig[currentEnv] || window.__CONFIG__.hospitalConfig.default
    // 设置全局配置
    var intAccount = hospitalConfig.intAccount
    var oAuthData = hospitalConfig.oAuthData || {}
    var energyEmodelId = hospitalConfig.energyEmodelId
    // 构建API路径配置
    const baseUrl = '<%= VUE_APP_UNIFIED_SERVER %>'
    const wsBaseUrl = '<%= VUE_APP_WS_SERVER %>'
    var __PATH = {
      ...Object.fromEntries(
        Object.entries(window.__CONFIG__.apiConfig).map(([key, path]) => [key, baseUrl + path])
      ),
      ...Object.fromEntries(
        Object.entries(window.__CONFIG__.wsConfig).map(([key, path]) => [key, wsBaseUrl + path])
      ),
      VUE_APP_HOSPITAL_NODE_ENV: currentEnv,
      VUE_ENERGY_API: '<%= VUE_APP_ENERGY_API %>' + 'api/',
      VUE_MINIO_API: '<%= VUE_APP_MINIO_SERVER %>',
      DICTIONAR_URL: '<%= VUE_APP_DICTIONAR_SERVER %>',
      WS_NEWS_API: '<%= VUE_APP_NEWS_WS_SERVER %>',
      VUE_APP_RTSP_LIVE_WS_SERVER: '<%= VUE_APP_RTSP_LIVE_WS_SERVER %>',
      VUE_APP_IEMS_IOMS_API: '<%= VUE_APP_IEMS_IOMS_API %>',
      VUE_APP_IEMS_API: '<%= VUE_APP_IEMS_API %>',
      BASE_URL: '<%= VUE_APP_BASE_URL_SUFFIX %>',
      OPEN_SIGNATURE: true,
      ...window.__CONFIG__.otherConfig,
      VUE_IEMC_API: 'http://**************:8191/'
    }
  </script>
</body>

</html>