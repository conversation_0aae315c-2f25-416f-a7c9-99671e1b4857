<template>
  <PageContainer :footer="true">
    <div slot="content" class="form-content">
      <div class="form-title"><i class="el-icon-arrow-left" @click="$router.go(-1)"></i>{{ pageTitle }}</div>
      <div class="content-main"></div>
    </div>
    <div slot="footer">
      <el-button style="padding: 8px 22px" @click="$router.go(-1)">取消</el-button>
      <el-button type="primary" @click="submitForm">确认</el-button>
    </div>
  </PageContainer>
</template>
<script>
var pageTitle
export default {
  name: '',
  // 路由前置守卫动态修改meta中的title
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const typeList = {
        add: '新增',
        edit: '编辑'
      }
      pageTitle = typeList[to.query.type] ?? '新增'
      to.meta.title = pageTitle
    }
    next()
  },
  data() {
    return {
      pageTitle
    }
  },
  computed: {},
  created() {},
  methods: {
    submitForm() {
      console.log(1)
    }
  }
}
</script>
<style lang="scss" scoped>
.form-content {
  height: 100%;
  overflow-y: auto;
  .form-title {
    height: 40px;
    line-height: 40px;
    background: #fff;
    border-bottom: 1px solid #dcdfe6;
    font-size: 15px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    font-weight: 500;
    color: #121f3e;
    padding-left: 15px;
    i {
      width: 20px;
      cursor: pointer;
    }
  }
  .content-main {
    height: calc(100% - 40px);
    width: 100%;
    background: #fff;
    padding: 16px 0px 0px 0px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
}
</style>
